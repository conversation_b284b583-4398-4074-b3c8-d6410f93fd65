import 'dart:ui';

import 'package:korrency/core/core.dart';

class BusyOverlay extends StatefulWidget {
  final Widget child;
  final bool show;
  final Color? bgColor;

  const BusyOverlay(
      {super.key, required this.child, this.bgColor, this.show = false});

  @override
  State<BusyOverlay> createState() => _BusyOverlayState();
}

class _BusyOverlayState extends State<BusyOverlay> {
  @override
  Widget build(BuildContext context) {
    return Material(
      child: SizedBox(
        width: Sizer.screenWidth,
        height: Sizer.screenWidth,
        child: Stack(
          children: <Widget>[
            widget.child,
            Visibility(
              visible: widget.show,
              child: BackdropFilter(
                filter: ImageFilter.blur(
                    sigmaX: 10, sigmaY: 10), // Adjust the blur intensity
                child: Container(
                  color: Colors.black12.withOpacity(
                      0.2), // Adjust the background color and opacity
                ),
              ),
            ),
            Center(
              child: IgnorePointer(
                ignoring: !widget.show,
                child: Visibility(
                  visible: widget.show,
                  child: Container(
                    width: Sizer.width(80),
                    height: Sizer.width(80),
                    padding: EdgeInsets.all(Sizer.radius(10)),
                    decoration: BoxDecoration(
                      color:
                          widget.bgColor ?? AppColors.userBg.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const LogoLoader(),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class BusyOverlayWhiteBg extends StatefulWidget {
  const BusyOverlayWhiteBg({
    super.key,
    required this.child,
    this.show = false,
  });

  final Widget child;
  final bool show;

  @override
  State<BusyOverlayWhiteBg> createState() => _BusyOverlayWhiteBgState();
}

class _BusyOverlayWhiteBgState extends State<BusyOverlayWhiteBg> {
  @override
  Widget build(BuildContext context) {
    return Material(
      child: SizedBox(
        width: Sizer.screenWidth,
        height: Sizer.screenWidth,
        child: Stack(
          children: <Widget>[
            widget.child,
            Visibility(
                visible: widget.show,
                child: Container(
                  color: AppColors.bgWhite,
                )),
            Visibility(
              visible: widget.show,
              child: Container(
                color: AppColors.bgWhite,
                child: Center(
                  child: IgnorePointer(
                    ignoring: !widget.show,
                    child: Container(
                      width: Sizer.width(80),
                      height: Sizer.width(80),
                      padding: EdgeInsets.all(Sizer.radius(10)),
                      decoration: BoxDecoration(
                        color: AppColors.userBg.withOpacity(0.6),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const LogoLoader(),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class LogoLoader extends StatefulWidget {
  const LogoLoader({
    super.key,
    this.h,
    this.w,
  });

  final double? h;
  final double? w;

  @override
  State<LogoLoader> createState() => _LogoLoaderState();
}

class _LogoLoaderState extends State<LogoLoader>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 1000),
  )..repeat();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (BuildContext context, Widget? child) {
        return Transform.rotate(
          angle: _controller.value *
              2 *
              3.141592653589793, // Multiply by 2π for a full rotation
          child: child,
        );
      },
      child: svgHelper(
        AppSvgs.logo,
        height: Sizer.height(50),
        width: Sizer.width(50),
      ),

      // imageHelper(
      //   AppImages.iconLogo,
      //   height: Sizer.height(widget.h ?? 38),
      //   width: Sizer.width(widget.w ?? 38),
      // ),
    );
  }
}
