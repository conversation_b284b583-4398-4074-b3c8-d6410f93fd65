import 'package:flutter_svg/flutter_svg.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class DelayAlertSheet extends StatefulWidget {
  const DelayAlertSheet({super.key});

  @override
  State<DelayAlertSheet> createState() => _DelayAlertSheetState();
}

class _DelayAlertSheetState extends State<DelayAlertSheet> {
  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      // height: Sizer.screenHeight * 0.50,
      // padding: EdgeInsets.symmetric(
      //   horizontal: Sizer.width(16),
      // ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(10),
          InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            child: Container(
              alignment: Alignment.centerRight,
              child: Icon(
                Icons.close,
                size: Sizer.width(25),
                color: AppColors.gray500,
              ),
            ),
          ),
          const YBox(24),
          SvgPicture.asset(
            AppSvgs.delayAlart,
            height: Sizer.height(210),
          ),
          const YBox(24),
          Text(
            'Your Transaction Might Take Longer Than Usual',
            textAlign: TextAlign.center,
            style: AppTypography.text24.copyWith(
              fontWeight: FontWeight.w700,
              height: 1.3,
            ),
          ),
          const YBox(10),
          Text(
            'We’re currently experiencing a slight delay in processing transactions, don’t worry your funds are safe. Would you still like to continue?',
            textAlign: TextAlign.center,
            style: AppTypography.text14.copyWith(
              color: AppColors.textBlack800,
              height: 1.6,
            ),
          ),
          const YBox(40),
          Row(
            children: [
              Expanded(
                child: CustomBtn.solid(
                  onTap: () {
                    Navigator.pop(context, false);
                  },
                  height: 44,
                  isOutline: true,
                  textColor: AppColors.baseBlack,
                  outlineColor: AppColors.grayEB,
                  borderRadius: BorderRadius.circular(8),
                  text: "No",
                ),
              ),
              XBox(16),
              Expanded(
                child: CustomBtn.solid(
                  onTap: () {
                    Navigator.pop(context, true);
                  },
                  height: 44,
                  borderRadius: BorderRadius.circular(8),
                  text: "Yes",
                ),
              ),
            ],
          ),
          const YBox(30),
        ],
      ),
    );
  }
}
