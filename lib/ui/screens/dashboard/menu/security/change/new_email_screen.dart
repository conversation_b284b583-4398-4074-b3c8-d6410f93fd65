import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class NewEmailScreen extends StatefulWidget {
  const NewEmailScreen({super.key});

  @override
  State<NewEmailScreen> createState() => _NewEmailScreenState();
}

class _NewEmailScreenState extends State<NewEmailScreen> {
  final emailC = TextEditingController();
  final focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  dispose() {
    emailC.dispose();
    focusNode.dispose();
    super.dispose();
  }

  bool get emailIsValid {
    return emailC.text.isNotEmpty &&
        emailC.text.contains("@") &&
        emailC.text.contains(".") &&
        emailC.text.split('.').last.isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: NewCustomAppbar(
        showHeaderTitle: true,
        headerText: 'New Email',
      ),
      body: Stack(
        children: [
          ListView(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
              vertical: Sizer.height(10),
            ),
            children: [
              Text(
                "Your Korrency account information would be updated to this email",
                style: FontTypography.text16.withCustomColor(AppColors.gray93),
              ),
              YBox(30),
              CustomTextField(
                focusNode: focusNode,
                controller: emailC,
                labelText: "Email",
                showLabelHeader: true,
                borderRadius: Sizer.height(12),
                prefixIcon: Icon(
                  Iconsax.sms,
                  color: AppColors.gray500,
                  size: Sizer.height(20),
                ),
                errorText: emailC.text.trim().isNotEmpty && !emailIsValid
                    ? "Invalid Email"
                    : null,
                onChanged: (val) => setState(() {}),
              ),
              YBox(40),
            ],
          ),
          Positioned(
            bottom: 50,
            left: 0,
            right: 0,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
              child: CustomBtn.withChild(
                online: emailIsValid,
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
                onTap: () {
                  BsWrapper.bottomSheet(
                    context: context,
                    widget: ConfirmEmailModal(
                      email: emailC.text.trim(),
                    ),
                  );
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Continue',
                      style: FontTypography.text15.medium
                          .withCustomColor(AppColors.white),
                    ),
                    const XBox(8),
                    const Icon(
                      Icons.arrow_forward,
                      color: AppColors.white,
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
