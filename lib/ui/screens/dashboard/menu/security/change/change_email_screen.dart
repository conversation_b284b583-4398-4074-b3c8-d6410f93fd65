// ignore_for_file: use_build_context_synchronously

import 'package:flutter/cupertino.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ChangeEmailScreen extends StatefulWidget {
  const ChangeEmailScreen({super.key});

  @override
  State<ChangeEmailScreen> createState() => _ChangeEmailScreenState();
}

class _ChangeEmailScreenState extends State<ChangeEmailScreen> {
  List<SecurityQuestion> secQuestions = [];
  late PageController _pageController;
  int _currentPageIndex = 0;
  List<TextEditingController> _answerControllers = [];
  List<bool> _answersValid = [];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadQuestions();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    for (var controller in _answerControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  _loadQuestions() async {
    final r = await context.read<SecQuestVM>().getUserSecQuestion(count: 3);
    if (r.success) {
      secQuestions = r.data;
      _initializeControllers();
      setState(() {});
    }
  }

  _initializeControllers() {
    _answerControllers = List.generate(
      secQuestions.length,
      (index) => TextEditingController(),
    );
    _answersValid = List.generate(secQuestions.length, (index) => false);

    // Add listeners to controllers for validation
    for (int i = 0; i < _answerControllers.length; i++) {
      _answerControllers[i].addListener(() => _validateAnswer(i));
    }
  }

  _validateAnswer(int index) {
    final isValid = _answerControllers[index].text.trim().isNotEmpty;
    if (_answersValid[index] != isValid) {
      _answersValid[index] = isValid;
      setState(() {});
    }
  }

  _checkForAnswers(int index) {
    final secVm = context.read<SecQuestVM>();
    secVm
        .validateSecQuestions(
      securityQuestionId: secQuestions[index].id ?? 0,
      answer: _answerControllers[index].text,
    )
        .then((v) {
      printty("value validateSecQuestions $v");
      if (_currentPageIndex > 0) {
        if (v.success && v.data?["data"]?["correct_answers"] >= 1) {
          Navigator.pushNamed(context, RoutePath.newEmailScreen);
          return;
        } else if (_currentPageIndex == secQuestions.length - 1) {
          if (v.success && v.data?["data"]?["correct_answers"] >= 2) {
            Navigator.pushNamed(context, RoutePath.newEmailScreen);
            return;
          } else {
            // Navigator.pushNamed(context, RoutePath.newEmailScreen);
            Navigator.pushNamed(
              context,
              RoutePath.changeConfirmationScreen,
              arguments: ChangeConfirmationArg(
                title: "Uh Oh!",
                subtitle: "Attempt unsuccessful!",
                desc:
                    "Sorry we are unable to change your email at this time, please try later",
                isSuccess: false,
              ),
            );
          }
        } else {
          _nextPage();
        }
        return;
      }
      _nextPage();
    });
  }

  _nextPage() {
    if (_currentPageIndex < secQuestions.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  _previousPage() {
    if (_currentPageIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  bool _isCurrentAnswerValid() {
    if (_currentPageIndex >= _answersValid.length) return false;
    return _answersValid[_currentPageIndex];
  }

  Widget _buildQuestionPage(int index) {
    final question = secQuestions[index];
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(16),
          horizontal: Sizer.width(28),
        ),
        decoration: BoxDecoration(
          color: AppColors.grayFE,
          borderRadius: BorderRadius.circular(Sizer.height(12)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Align(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "Question ${index + 1}",
                    style:
                        FontTypography.text12.withCustomColor(AppColors.gray93),
                  ),
                  YBox(8),
                  Container(
                    height: Sizer.height(1),
                    width: Sizer.width(90),
                    color: AppColors.grayEC,
                  ),
                ],
              ),
            ),
            YBox(20),
            Text(
              question.question ?? "Security Question",
              style: FontTypography.text22.semiBold,
            ),
            YBox(70),
            Text(
              "Your answer",
              style: FontTypography.text12.withCustomColor(AppColors.gray93),
            ),
            YBox(8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.grayAB),
                borderRadius: BorderRadius.circular(Sizer.height(12)),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                      left: Sizer.width(12),
                      top: Sizer.height(10),
                    ),
                    child: Icon(
                      Iconsax.message_edit,
                      color: AppColors.gray500,
                      size: Sizer.height(20),
                    ),
                  ),
                  Expanded(
                    child: CustomTextField(
                      controller: _answerControllers[index],
                      hideBorder: true,
                      borderRadius: Sizer.height(12),
                      maxLines: 3,
                      contentPadding: EdgeInsets.only(
                        left: Sizer.width(8),
                        top: Sizer.height(20),
                      ),
                      onChanged: (val) {
                        // Validation is handled by the controller listener
                      },
                    ),
                  ),
                ],
              ),
            ),
            YBox(40),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SecQuestVM>(builder: (ctx, vm, _) {
      return Scaffold(
        backgroundColor: AppColors.white,
        appBar: NewCustomAppbar(
          showHeaderTitle: true,
          headerText: 'Change Email',
        ),
        body: Builder(builder: (context) {
          if (vm.isBusy) {
            return const Center(child: CupertinoActivityIndicator());
          }

          if (secQuestions.isEmpty) {
            return Padding(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
                vertical: Sizer.height(10),
              ),
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      "Something went wrong, Please try again later",
                      style: FontTypography.text14.medium
                          .withCustomColor(AppColors.gray93),
                    ),
                    YBox(20),
                    CustomBtn.solid(
                      onTap: () {
                        _loadQuestions();
                      },
                      borderRadius: BorderRadius.circular(Sizer.radius(20)),
                      text: "Reload",
                    ),
                  ],
                ),
              ),
            );
          }
          return ListView(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(24),
                  vertical: Sizer.height(10),
                ),
                child: Text(
                  "To change your email, please provide the answer to the security questions you set",
                  style:
                      FontTypography.text16.withCustomColor(AppColors.gray93),
                ),
              ),
              YBox(10),
              Container(
                height: Sizer.height(360),
                padding: EdgeInsets.only(
                  top: Sizer.height(20),
                ),
                child: PageView.builder(
                  controller: _pageController,
                  physics: const NeverScrollableScrollPhysics(),
                  onPageChanged: (index) {
                    _currentPageIndex = index;
                    setState(() {});
                  },
                  itemCount: secQuestions.length,
                  itemBuilder: (context, index) {
                    return _buildQuestionPage(index);
                  },
                ),
              ),
              Padding(
                padding: EdgeInsets.only(
                  left: Sizer.width(24),
                  right: Sizer.width(24),
                  top: Sizer.height(150),
                  bottom: Sizer.height(30),
                ),
                child: CustomBtn.withChild(
                  borderRadius: BorderRadius.circular(Sizer.radius(20)),
                  online: _isCurrentAnswerValid() &&
                      !vm.busy(validateSecQuestionsState),
                  onTap: _isCurrentAnswerValid()
                      ? () {
                          _checkForAnswers(_currentPageIndex);
                        }
                      : null,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Continue',
                        style: FontTypography.text15.medium
                            .withCustomColor(AppColors.white),
                      ),
                      const XBox(8),
                      const Icon(
                        Icons.arrow_forward,
                        color: AppColors.white,
                        size: 20,
                      ),
                      if (vm.busy(validateSecQuestionsState)) const XBox(20),
                      if (vm.busy(validateSecQuestionsState))
                        const CupertinoActivityIndicator()
                    ],
                  ),
                ),
              ),
              YBox(30),
            ],
          );
        }),
      );
    });
  }
}
