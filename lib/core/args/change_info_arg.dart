import 'package:korrency/core/core.dart';

class ChangeInfoArg {
  ChangeInfoArg({
    required this.code,
    required this.recipient,
    required this.accountChangeType,
  });

  final String code;
  final String recipient;
  final AccountChangeType accountChangeType;

  Map<String, dynamic> toJson() => {
        "code": code,
        "recipient": recipient,
        "verification_type":
            accountChangeType.verification, // email_change or phone_change
      };
}
