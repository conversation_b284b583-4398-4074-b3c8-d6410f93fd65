import 'dart:convert';

import 'package:korrency/core/core.dart';

class SecConst {
  static const String sec1 = "sec1";
  static const String sec2 = "sec2";
  static const String sec3 = "sec3";
  static const String user = "user";

  static const String sec1Answer = "sec1Answer";
  static const String sec2Answer = "sec2Answer";
  static const String sec3Answer = "sec3Answer";
  static const String userAnswer = "userAnswer";
}

const String validateSecQuestionsState = "validateSecQuestionsState";

class SecQuestVM extends BaseVM {
  Map<String, TextEditingController> questionControllers = {
    SecConst.sec1: TextEditingController(),
    SecConst.sec2: TextEditingController(),
    SecConst.sec3: TextEditingController(),
    SecConst.user: TextEditingController(),
  };

  Map<String, TextEditingController> answersControllers = {
    SecConst.sec1Answer: TextEditingController(),
    SecConst.sec2Answer: TextEditingController(),
    SecConst.sec3Answer: TextEditingController(),
    SecConst.userAnswer: TextEditingController(),
  };

  final Map<String, int> _questionIds = {};
  Map<String, int> get questionIds => _questionIds;

  List<SecurityQuestion> _secQuestions = [];
  List<SecurityQuestion> get secQuestions => _secQuestions;

  List<SecurityQuestion> _userSecQuestions = [];
  List<SecurityQuestion> get userSecQuestions => _userSecQuestions;

  // Method to set a question
  void setQuestion(String key, String quest) {
    questionControllers[key]?.text = quest;
    reBuildUI();
  }

  // Method to set a questiion id
  void setQuestionId(String key, int id) {
    _questionIds[key] = id;
    reBuildUI();
  }

  // Method to get a list of selected questions
  List<String> get selectedQuestionsList =>
      questionControllers.values.map((controller) => controller.text).toList();

  // Method to get a list of available questions
  List<SecurityQuestion> get getAvailableQuestions {
    List<String> selectedQuests = selectedQuestionsList;
    List<SecurityQuestion> availableQuestions = secQuestions
        .where((question) => !selectedQuests.contains(question.question))
        .toList();

    return availableQuestions;
  }

  bool get btnIsActive {
    var filteredQuestControllers = questionControllers.entries
        .where((entry) => entry.key != SecConst.user)
        .map((entry) => entry.value);
    var filteredAnswerControllers = answersControllers.entries
        .where((entry) => entry.key != SecConst.userAnswer)
        .map((entry) => entry.value);

    // Check if every remaining controller has a non-empty text
    return filteredQuestControllers
            .every((controller) => controller.text.isNotEmpty) &&
        filteredAnswerControllers
            .every((controller) => controller.text.isNotEmpty);
  }

  // bool get btnIsActive {
  //   return questionControllers.values
  //           .every((controller) => controller.text.isNotEmpty) &&
  //       answersControllers.values
  //           .every((controller) => controller.text.isNotEmpty);
  // }

  Future<ApiResponse> setSecQuestions() async {
    return await performApiCall(
      url: "/auth/security-questions",
      method: apiService.postWithAuth,
      body: {
        "security_questions": [
          {
            "security_question_id": _questionIds[SecConst.sec1],
            "answer": answersControllers[SecConst.sec1Answer]?.text,
          },
          {
            "security_question_id": _questionIds[SecConst.sec3],
            "answer": answersControllers[SecConst.sec2Answer]?.text,
          },
          {
            "security_question_id": _questionIds[SecConst.sec3],
            "answer": answersControllers[SecConst.sec3Answer]?.text,
          }
        ]
      },
      onSuccess: (data) {
        return ApiResponse(success: true, data: apiResponse);
      },
    );
  }

  Future<ApiResponse> getUserSecQuestion({int? count}) async {
    UriBuilder uriBuilder = UriBuilder("/auth/security-questions/user");
    if (count != null) {
      uriBuilder.addQueryParameter("count", count.toString());
    }
    return await performApiCall(
      url: uriBuilder.build().toString(),
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _userSecQuestions = securityQuestionFromJson(json.encode(data["data"]));
        questionControllers[SecConst.user]?.text =
            _userSecQuestions.first.question ??
                "No security question found for this user";

        printty("User Sec Questions: ${_userSecQuestions.first.id}");

        return ApiResponse(success: true, data: _userSecQuestions);
      },
    );
  }

  Future<ApiResponse> confirmAnswerToSecQuestion() async {
    return await performApiCall(
      url: "/auth/security-questions/confirm-answers",
      method: apiService.postWithAuth,
      body: {
        "user_security_questions": [
          {
            "user_security_question_id": _userSecQuestions.first.id,
            "answer": answersControllers[SecConst.userAnswer]?.text,
          }
        ]
      },
      onSuccess: (data) {
        return ApiResponse(
          success: true,
          message: apiResponse.message,
        );
      },
    );
  }

  Future<ApiResponse> validateSecQuestions({
    required int securityQuestionId,
    required String answer,
  }) async {
    return await performApiCall(
      url: "/auth/security-questions/confirm-answers-v2",
      method: apiService.postWithAuth,
      busyObjectName: validateSecQuestionsState,
      body: {
        "user_security_questions": [
          {
            "user_security_question_id": securityQuestionId,
            "answer": answer,
          }
        ]
      },
      onSuccess: (data) {
        return ApiResponse(
          success: true,
          data: data,
        );
      },
    );
  }

  Future<ApiResponse> getSecurityQuestions() async {
    return await performApiCall(
      url: "/auth/security-questions",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _secQuestions = securityQuestionFromJson(json.encode(data["data"]));
        return ApiResponse(success: true, data: _secQuestions);
      },
    );
  }

  clearData() {
    for (var controller in questionControllers.values) {
      controller.clear();
    }
    for (var controller in answersControllers.values) {
      controller.clear();
    }
  }

  @override
  void dispose() {
    printty("SecQuest Dispose called");
    for (var controller in questionControllers.values) {
      controller.dispose();
    }
    for (var controller in answersControllers.values) {
      controller.dispose();
    }

    super.dispose();
  }
}
