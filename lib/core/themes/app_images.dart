import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

const String images = "assets/images";

class AppImages {
  static const logo = "$images/logo.png";
  static const noimage = "$images/noimage.jpg";
  static const iconLogo = "$images/iconLogo.png";
  static const logoText = "$images/logoText.png";
  static const update = "$images/update.png";
  static const splashBg = "$images/splashBg.png";
  static const smGradient = "$images/smGradient.png";

  static const fingerPrint = "$images/fingerPrint.png";
  static const fingerprint1 = "$images/fingerprint1.png";
  static const faceId = "$images/faceId.png";

  static const circleCheck = "$images/circleCheck.png";
  static const circleError = "$images/circleError.png";
  static const success = "$images/success.png";
  static const failed = "$images/failed.png";

  static const ngn = "$images/ngn.png";
  static const cad = "$images/cad.png";
  static const identity = "$images/identity.png";

  static const phoneUser = "$images/phoneUser.png";
  static const user = "$images/user.png";
  static const coins = "$images/coins.png";
  static const team = "$images/team.png";

  // Dashboard
  static const fab = "$images/dashboard/fab.png";
  static const avater = "$images/dashboard/avater.png";
  static const pattern = "$images/dashboard/pattern.png";

  // Onboard
  static const onboard0 = "$images/onboard/onboard0.png";
  static const onboard1 = "$images/onboard/onboard1.png";
  static const onboard2 = "$images/onboard/onboard2.png";
  static const onboard3 = "$images/onboard/onboard3.png";

  static const gtb = "$images/gtb.png";
  static const transaction = "$images/transaction.png";
  static const contactUs = "$images/contactUs.png";
  static const warning = "$images/warning.png";
  static const trustedDevice = "$images/trustedDevice.png";

  static const howitworks = "$images/howitworks.png";
  static const p2p = "$images/p2p.png";
  static const trash = "$images/trash.png";
  static const rateAlert = "$images/rateAlert.png";
  static const interac = "$images/interac.png";
  static const envelope = "$images/envelope.png";
  static const interacK = "$images/inerac-k.png";
  static const edu = "$images/edu.jpg";
  static const occupation = "$images/occupation.png";
  static const newOffer = "$images/newOffer.png";
}

// Image Helper
SizedBox imageHelper(String image,
    {double? height, double? width, BoxFit? fit}) {
  return SizedBox(
    height: height,
    width: width,
    child: Image.asset(
      image,
      fit: fit,
    ),
  );
}

Widget cacheNetWorkImage(
  String url, {
  double? height,
  double? width,
  BoxFit? fit,
}) {
  return CachedNetworkImage(
    imageUrl: url,
    width: width,
    height: height,
    fit: fit,
    fadeInDuration: const Duration(milliseconds: 2000),
    placeholder: (context, url) {
      return const SizedBox();
    },
    errorWidget: (context, url, error) {
      return const SizedBox();
    },
  );
}
