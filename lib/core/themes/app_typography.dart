import 'package:google_fonts/google_fonts.dart';
import 'package:korrency/core/core.dart';

class AppTypography {
  static TextStyle text8 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(8),
  );

  static TextStyle text10 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(10),
  );

  static TextStyle text11 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(11),
    letterSpacing: 0.1,
  );

  static TextStyle text12 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(12),
    letterSpacing: 0.1,
  );

  static TextStyle text12b = TextStyle(
    fontWeight: FontWeight.bold,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(12),
    letterSpacing: 0.1,
  );

  static TextStyle text13 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(13),
  );

  static TextStyle text14 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(14),
  );

  static TextStyle text14b = TextStyle(
    fontWeight: FontWeight.bold,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(14),
  );
  static TextStyle text15 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(15),
  );

  static TextStyle text16 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(16),
  );

  static TextStyle text18 = TextStyle(
    fontWeight: FontWeight.normal,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(18),
  );

  static TextStyle text20 = TextStyle(
    color: AppColors.baseBlack,
    fontSize: Sizer.text(20),
  );
  static TextStyle text20b = TextStyle(
    fontWeight: FontWeight.bold,
    color: AppColors.baseBlack,
    fontSize: Sizer.text(20),
  );

  static TextStyle text22 = TextStyle(
    color: AppColors.baseBlack,
    fontSize: Sizer.text(22),
  );

  static TextStyle text24 = TextStyle(
    color: AppColors.baseBlack,
    fontSize: Sizer.text(24),
  );

  static TextStyle text28 = TextStyle(
    color: AppColors.baseBlack,
    fontSize: Sizer.text(28),
  );

  static TextStyle text32 = TextStyle(
    color: AppColors.baseBlack,
    height: 1.2,
    fontSize: Sizer.text(32),
  );

  static TextStyle text36 = TextStyle(
    color: AppColors.baseBlack,
    height: 1.2,
    fontSize: Sizer.text(36),
  );
}

class FontTypography {
  // Default color for all text styles
  static final Color _defaultColor = AppColors.mainBlack;

  // Base method to create TextStyle with common properties
  static TextStyle _createTextStyle({
    required double fontSize,
    FontWeight fontWeight = FontWeight.normal,
    Color? color,
    double? height,
  }) {
    return GoogleFonts.outfit(
      fontWeight: fontWeight,
      color: color ?? _defaultColor,
      fontSize: Sizer.text(fontSize),
      height: height,
    );
  }

  // Font size definitions
  static TextStyle text8 = _createTextStyle(fontSize: 8);
  static TextStyle text10 = _createTextStyle(fontSize: 10);
  static TextStyle text12 = _createTextStyle(fontSize: 12);
  static TextStyle text13 = _createTextStyle(fontSize: 13);
  static TextStyle text14 = _createTextStyle(fontSize: 14);
  static TextStyle text15 = _createTextStyle(fontSize: 15);
  static TextStyle text16 = _createTextStyle(fontSize: 16);
  static TextStyle text18 = _createTextStyle(fontSize: 18);
  static TextStyle text20 = _createTextStyle(fontSize: 20);
  static TextStyle text22 = _createTextStyle(fontSize: 22);
  static TextStyle text24 = _createTextStyle(fontSize: 24);
  static TextStyle text26 = _createTextStyle(fontSize: 26);
  static TextStyle text28 = _createTextStyle(fontSize: 28);
  static TextStyle text30 = _createTextStyle(fontSize: 30);
  static TextStyle text32 = _createTextStyle(fontSize: 32);
  static TextStyle text36 = _createTextStyle(fontSize: 36);
  static TextStyle text48 = _createTextStyle(fontSize: 48);

  // Utility method to recreate a style with modifications
  static TextStyle _recreateStyle(
    TextStyle baseStyle, {
    FontWeight? fontWeight,
    Color? color,
    double? height,
  }) {
    return GoogleFonts.roboto(
      fontWeight: fontWeight ?? baseStyle.fontWeight,
      color: color ?? baseStyle.color,
      fontSize: baseStyle.fontSize,
      height: height ?? baseStyle.height,
    );
  }

  // Variation methods using the utility method
  static TextStyle withBold(TextStyle baseStyle) {
    return _recreateStyle(baseStyle, fontWeight: FontWeight.bold);
  }

  static TextStyle withSemiBold(TextStyle baseStyle) {
    return _recreateStyle(baseStyle, fontWeight: FontWeight.w600);
  }

  static TextStyle withMedium(TextStyle baseStyle) {
    return _recreateStyle(baseStyle, fontWeight: FontWeight.w500);
  }

  static TextStyle withColor(TextStyle baseStyle, Color color) {
    return _recreateStyle(baseStyle, color: color);
  }

  static TextStyle withHeight(TextStyle baseStyle, double height) {
    return _recreateStyle(baseStyle, height: height);
  }
}

// Usage examples:
// Basic usage: AppTypography.text16
// With modification: AppTypography.withBold(AppTypography.text16)
// With color: AppTypography.withColor(AppTypography.text16, AppColors.primary)
// Chained modifications:
// AppTypography.withColor(AppTypography.withBold(AppTypography.text16), AppColors.primary)

// Shorthand extension methods for more fluent usage
extension TextStyleExtensions on TextStyle {
  TextStyle get bold => FontTypography.withBold(this);
  TextStyle get medium => FontTypography.withMedium(this);
  TextStyle get semiBold => FontTypography.withSemiBold(this);
  TextStyle withCustomColor(Color color) =>
      FontTypography.withColor(this, color);
  TextStyle withCustomHeight(double height) =>
      FontTypography.withHeight(this, height);
}
